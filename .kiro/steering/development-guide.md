---
inclusion: always
---

# 开发指南

## 关键约束

### 网格系统要求

- 渲染 33x33 矩阵（1089 个单元格），不使用虚拟化
- 使用基于 0 的索引（33x33 网格为 0-32）
- 支持 8 种颜色类别：红、青、黄、紫、橙、绿、蓝、粉
- 处理 4 个数据层级（Level 1-4），带整数验证
- 在无虚拟化情况下实现实时渲染性能

### 技术栈

- **前端**: Next.js 15.1.0 (App Router), React 18.3.1, TypeScript 5.8.3 (严格模式)
- **状态**: Zustand 5.0.6 配合持久化中间件
- **数据**: @tanstack/react-query 5.83.0 用于 API 调用，Prisma 6.11.1 用于数据库
- **测试**: Vitest 3.2.4 (单元测试)，Playwright 1.54.0 (端到端测试)
- **后端**: FastAPI 0.116.1 配合 SQLModel 0.0.24, Python 3.11+

## 架构模式

### 状态管理 (Zustand)

- **basicDataStore**: 网格数据、单元格坐标、颜色映射、矩阵数据管理
- **styleStore**: UI 配置、主题、显示偏好、样式预设管理
- **dynamicStyleStore**: 计算样式、缓存计算、响应式样式、交互状态
- **gridConfigStore**: 网格配置、显示模式、灰色模式管理
- 为网格状态和用户偏好使用持久化中间件，支持版本迁移
- 绝不在同一存储中混合 UI 状态和业务数据

### 组件架构

- 用 React.memo 包装所有组件
- 分离容器组件（业务逻辑）和展示组件（仅显示）
- 为网格系统故障实现错误边界
- 对复杂 UI 使用复合组件模式（>3 个相关子组件）

### 功能组织

```text
features/[feature-name]/
├── components/     # 功能特定的 UI 组件
├── hooks/         # 业务逻辑钩子
├── store/         # 功能状态管理
├── types/         # TypeScript 定义
├── utils/         # 功能工具函数
└── index.ts       # 仅桶导出
```

## 代码约定

### 命名标准

- **组件**: PascalCase (GridContainer.tsx, ControlPanelProvider.tsx)
- **钩子**: camelCase 带 'use' 前缀 (useCellDataManager.ts, useValidation.ts)
- **存储**: camelCase 带 'Store' 后缀 (basicDataStore.ts, styleStore.ts)
- **类型/接口**: PascalCase (CellData, GridConfig, ApiResponse)
- **常量**: SCREAMING_SNAKE_CASE (MAX_GRID_SIZE, DEFAULT_CELL_COLOR)
- **目录**: 功能用 kebab-case，技术目录用 camelCase

### 文件组织

- 始终从 index.ts 文件使用桶导出
- 始终优先使用命名导出而非默认导出
- 绝不创建循环依赖
- 绝不将功能嵌套超过 2 层深度

### 导入组织

```typescript
// 1. 外部库
import React from 'react'
import { useQuery } from '@tanstack/react-query'

// 2. 内部工具和类型
import type { GridConfig } from '@/lib/types'
import { cn } from '@/lib/utils'

// 3. 功能导入
import { useGridData } from '../hooks'
```

## 性能规则

### 渲染优化

- 对 >10 行的计算使用 useMemo
- 对传递给子组件的事件处理器使用 useCallback
- 防抖用户交互（最少 100ms）
- 批量相关状态更新以防止渲染抖动
- 绝不创建无法处理完整 1089 单元格网格负载的组件

### 内存管理

- 缓存计算值以避免重复计算
- 在 useEffect 钩子中实现适当的清理
- 使用 React.memo 防止不必要的重新渲染

## 决策树

### 功能模块创建

```text
功能是否被 >1 个页面使用？
├─ 是 → 在 features/ 中创建
└─ 否 → 保留在页面特定的 components/ 中

功能是否有 >3 个相关组件？
├─ 是 → 创建完整功能结构
└─ 否 → 创建简单组件目录
```

### 状态管理

```text
状态是否在 >2 个组件间共享？
├─ 是 → 使用 Zustand 存储
└─ 否 → 使用本地 useState

状态是否需要持久化？
├─ 是 → 添加持久化中间件
└─ 否 → 使用仅内存存储

状态是否从其他状态计算而来？
├─ 是 → 使用 dynamicStyleStore 模式
└─ 否 → 使用直接状态存储
```

## 基本命令

```bash
# Monorepo
pnpm install              # 安装所有依赖
pnpm run dev              # 启动所有应用
pnpm run build            # 构建所有应用
pnpm run lint             # 检查所有应用代码
pnpm run test             # 运行所有测试
pnpm run clean            # 清理构建缓存

# 前端 (apps/frontend)
pnpm run dev              # 开发服务器 (localhost:4096)
pnpm run type-check       # TypeScript 验证
pnpm run db:studio        # Prisma Studio
pnpm run test             # 运行单元测试
pnpm run test:e2e         # 运行端到端测试
pnpm run naming:check     # 检查命名规范
pnpm run naming:fix       # 修复命名问题

# 后端 (apps/backend)
poetry install            # 安装 Python 依赖
poetry run uvicorn app.main:app --reload  # 开发服务器 (localhost:8000)
```

## 质量标准

### 错误处理

- 为功能模块实现错误边界
- 为失败的组件提供回退 UI
- 记录错误时提供足够的调试上下文
- 绝不让错误导致整个应用程序崩溃

### 测试要求

- 测试完整 1089 单元格网格渲染
- 验证数据持久化和同步
- 为关键 UI 实现视觉回归测试
- 使用 Playwright 进行端到端测试，Vitest 进行单元测试

### TypeScript 标准

- 在所有配置中启用严格模式
- 根据定义的模式验证所有用户输入
- 为失败的 API 调用实现重试逻辑
- 为异步操作使用适当的错误类型
