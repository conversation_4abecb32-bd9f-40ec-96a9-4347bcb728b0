/**
 * 基础数据Store - 管理A到M组矩阵数据的统一存储
 * 🎯 核心职责：统一的A-M组数据管理、状态管理、可见性控制
 * 📦 重构优化：精简为核心Store逻辑，其他功能已分离到专门模块
 * ✅ 职责分离：只保留状态管理，计算和验证逻辑已迁移
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { useHydrationActions } from '@/lib/stores/HydrationManager';

// 导入重构后的类型和常量
import type {
  BasicColorType,
  GroupType,
  ColorLevel,
  MatrixDataPoint,
  MatrixData,
  CellData,
  ColorValue,
  ColorVisibility,
  BlackCellData,
  BasicDataStore
} from '@/lib/types/matrix';

import {
  AVAILABLE_LEVELS,
  DEFAULT_COLOR_VALUES
} from '@/stores/constants/matrix';

import {
  generateMatrixData,
  generateGridData
} from '@/lib/utils/matrixUtils';

import {
  generateDefaultColorVisibility,
  generateDefaultGroupVisibility,
  generateDefaultBlackCellData,
  getAllColorTypes,
  getAllGroupTypes,
  getAvailableLevels
} from '@/lib/utils/matrixHelpers';

import {
  createCustomStorage,
  createMigrationFunction
} from '@/lib/utils/storageUtils';

// 生成默认值
const DEFAULT_COLOR_VISIBILITY = generateDefaultColorVisibility();
const DEFAULT_BLACK_CELL_DATA = generateDefaultBlackCellData();

// 重新导出类型供其他模块使用
export type {
  BasicColorType,
  GroupType,
  ColorLevel,
  MatrixDataPoint,
  MatrixData,
  CellData,
  ColorValue,
  ColorVisibility,
  BlackCellData
};

// 创建自定义存储配置
const customStorage = createCustomStorage();

export const useBasicDataStore = create<BasicDataStore>()(
  persist(
    (set, get) => ({
      // 初始状态 - 延迟生成数据，避免与persist恢复冲突
      matrixData: null as MatrixData | null,
      gridData: generateGridData(),
      colorValues: DEFAULT_COLOR_VALUES,
      colorVisibility: DEFAULT_COLOR_VISIBILITY,
      colorLevelRules: AVAILABLE_LEVELS,
      blackCellData: DEFAULT_BLACK_CELL_DATA,
      groupVisibility: generateDefaultGroupVisibility(),

      // 简化的hydration状态跟踪
      _isHydrated: false,

      // 矩阵数据操作
      regenerateMatrixData: () => {
        const newMatrixData = generateMatrixData();
        set(() => ({ matrixData: newMatrixData }));
      },

      initializeMatrixData: () => {
        process.env.NODE_ENV === 'development' && console.log('🔍 [Store] initializeMatrixData 被调用');
        const state = get();

        // 检查数据完整性：不仅要存在，还要有有效数据
        const hasValidData = state.matrixData &&
                            state.matrixData.byCoordinate &&
                            state.matrixData.byCoordinate.size > 0;

        process.env.NODE_ENV === 'development' && console.log('🔍 [Store] 当前状态:', {
          hasMatrixData: !!state.matrixData,
          hasValidData,
          coordinateSize: state.matrixData?.byCoordinate?.size || 0
        });

        if (!hasValidData) {
          process.env.NODE_ENV === 'development' && console.log('🔍 [Store] 没有有效矩阵数据，开始生成');
          const newMatrixData = generateMatrixData(true);
          process.env.NODE_ENV === 'development' && console.log('🔍 [Store] 生成的数据:', {
            hasData: !!newMatrixData,
            byCoordinateSize: newMatrixData?.byCoordinate?.size || 0
          });

          set(() => ({ matrixData: newMatrixData, _isHydrated: true }));
          process.env.NODE_ENV === 'development' && console.log('🔍 [Store] 数据已设置到store');
        } else {
          process.env.NODE_ENV === 'development' && console.log('🔍 [Store] 有效矩阵数据已存在，跳过初始化');
          // 确保hydrated状态正确
          if (!state._isHydrated) {
            set(() => ({ _isHydrated: true }));
          }
        }
      },

      getMatrixData: () => get().matrixData,

      getDataPointsAt: (x, y) => {
        const matrixData = get().matrixData;
        if (!matrixData) return [];
        const coordKey = `${x},${y}`;
        return matrixData.byCoordinate.get(coordKey) || [];
      },

      getGroupData: (group) => {
        const matrixData = get().matrixData;
        if (!matrixData) return [];
        return matrixData.byGroup[group] || [];
      },

      getColorData: (color) => {
        const matrixData = get().matrixData;
        if (!matrixData) return [];
        return matrixData.byColor[color] || [];
      },

      getLevelData: (level) => {
        const matrixData = get().matrixData;
        if (!matrixData) return [];
        return matrixData.byLevel[level] || [];
      },

      // 网格数据操作
      regenerateGridData: () => {
        const newGridData = generateGridData();
        set(() => ({ gridData: newGridData }));
      },

      getGridData: () => get().gridData,

      updateCellData: (index, updates) =>
        set((state) => {
          const newGridData = [...state.gridData];
          if (index >= 0 && index < newGridData.length) {
            newGridData[index] = { ...newGridData[index], ...updates };
          }
          return { gridData: newGridData };
        }),

      getCellAt: (x, y) => {
        const gridData = get().gridData;
        return gridData.find(cell => cell.x === x && cell.y === y) || null;
      },

      // 颜色值操作
      updateColorValues: (colorType, values) =>
        set((state) => ({
          colorValues: {
            ...state.colorValues,
            [colorType]: { ...state.colorValues[colorType], ...values },
          },
        })),

      getColorValue: (colorType) => get().colorValues[colorType],

      // 可见性控制
      setColorVisibility: (colorType, visibility) =>
        set((state) => ({
          colorVisibility: {
            ...state.colorVisibility,
            [colorType]: { ...state.colorVisibility[colorType], ...visibility },
          },
        })),

      toggleColorLevel: (colorType, level) =>
        set((state) => {
          const currentVisibility = state.colorVisibility[colorType];
          const levelKey = `showLevel${level}` as keyof ColorVisibility;

          if (levelKey in currentVisibility) {
            return {
              colorVisibility: {
                ...state.colorVisibility,
                [colorType]: {
                  ...currentVisibility,
                  [levelKey]: !currentVisibility[levelKey],
                },
              },
            };
          }
          return state;
        }),

      setGroupVisibility: (group, visible) =>
        set((state) => ({
          groupVisibility: {
            ...state.groupVisibility,
            [group]: visible,
          },
        })),

      toggleGroupVisibility: (group) =>
        set((state) => ({
          groupVisibility: {
            ...state.groupVisibility,
            [group]: !state.groupVisibility[group],
          },
        })),

      // 黑色格子数据
      setBlackCellData: (data) =>
        set(() => ({ blackCellData: data })),

      toggleBlackCellVisibility: () =>
        set((state) => ({
          blackCellData: {
            ...state.blackCellData,
            visibility: !state.blackCellData.visibility,
          },
        })),

      // 批量操作
      toggleAllColorCells: (show) =>
        set((state) => {
          const newVisibility: Record<BasicColorType, ColorVisibility> = {} as any;
          Object.keys(state.colorVisibility).forEach((colorType) => {
            newVisibility[colorType as BasicColorType] = {
              ...state.colorVisibility[colorType as BasicColorType],
              showCells: show,
            };
          });
          return { colorVisibility: newVisibility };
        }),

      toggleAllGroups: (show) =>
        set((state) => {
          const newGroupVisibility: Record<GroupType, boolean> = {} as any;
          Object.keys(state.groupVisibility).forEach((group) => {
            newGroupVisibility[group as GroupType] = show;
          });
          return { groupVisibility: newGroupVisibility };
        }),

      resetToDefaults: () => {
        // 生成新的矩阵数据，确保与默认可见性配置匹配
        const newMatrixData = generateMatrixData(true);
        const newColorVisibility = generateDefaultColorVisibility();
        const newGroupVisibility = generateDefaultGroupVisibility();

        set(() => ({
          matrixData: newMatrixData,
          gridData: generateGridData(),
          colorValues: DEFAULT_COLOR_VALUES,
          colorVisibility: newColorVisibility,
          colorLevelRules: AVAILABLE_LEVELS,
          blackCellData: DEFAULT_BLACK_CELL_DATA,
          groupVisibility: newGroupVisibility,
          _isHydrated: true,
        }));
      },

      // 常量访问器
      getAvailableLevels: (colorType) => getAvailableLevels(colorType),
      getAllColorTypes: () => getAllColorTypes(),
      getAllGroupTypes: () => getAllGroupTypes(),


    }),
    {
      name: 'basic-data-store',
      version: 4, // 版本升级，重构为新的矩阵数据结构
      storage: customStorage, // 使用自定义存储来处理 Map 对象
      migrate: createMigrationFunction(generateMatrixData),
    }
  )
);

// 新的选择器函数 - 基于统一的矩阵数据结构
export const useMatrixData = () => useBasicDataStore((state) => state.matrixData);

export const useColorValues = (colorType?: BasicColorType) =>
  useBasicDataStore((state) =>
    colorType ? state.colorValues[colorType] : state.colorValues
  );

export const useColorVisibility = (colorType?: BasicColorType) =>
  useBasicDataStore((state) =>
    colorType ? state.colorVisibility[colorType] : state.colorVisibility
  );

export const useGroupVisibility = (group?: GroupType) =>
  useBasicDataStore((state) =>
    group ? state.groupVisibility[group] : state.groupVisibility
  );

export const useBlackCellData = () =>
  useBasicDataStore((state) => state.blackCellData);

// 矩阵数据访问选择器
export const useDataPointsAt = (x: number, y: number) =>
  useBasicDataStore((state) => state.getDataPointsAt(x, y));

export const useGroupData = (group: GroupType) =>
  useBasicDataStore((state) => state.getGroupData(group));

export const useColorData = (color: BasicColorType) =>
  useBasicDataStore((state) => state.getColorData(color));

export const useLevelData = (level: 1 | 2 | 3 | 4) =>
  useBasicDataStore((state) => state.getLevelData(level));

// 网格数据访问选择器
export const useGridData = () =>
  useBasicDataStore((state) => state.gridData);

export const useCellAt = (x: number, y: number) =>
  useBasicDataStore((state) => state.getCellAt(x, y));



